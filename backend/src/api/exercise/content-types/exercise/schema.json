{"kind": "collectionType", "collectionName": "exercises", "info": {"singularName": "exercise", "pluralName": "exercises", "displayName": "Exercise"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "order": {"type": "integer"}, "content": {"type": "blocks"}, "chapter": {"type": "relation", "relation": "manyToOne", "target": "api::chapter.chapter", "inversedBy": "exercises"}}}