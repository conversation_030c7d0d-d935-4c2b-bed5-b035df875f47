"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";

const API_BASE_URL = "https://provinces.open-api.vn/api";

const LocationSelector = ({ onAddressChange, onClose, currentAddress }) => {
  const [searchText, setSearchText] = useState("");
  const [activeTab, setActiveTab] = useState("province");
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProvince, setSelectedProvince] = useState(null);
  const [selectedDistrict, setSelectedDistrict] = useState(null);
  const [error, setError] = useState(null);
  const locationSelectorRef = useRef(null);
  const timeoutIdRef = useRef(null);

  const clearLocationData = () => {
    setDistricts([]);
    setWards([]);
    setSelectedDistrict(null);
  };

  const handleSearchChange = useCallback((e) => {
    setSearchText(e.target.value);
  }, []);

  const handleTabChange = useCallback(
    (tab) => {
      setActiveTab(tab);
      if (tab === "province") {
        clearLocationData();
      } else if (tab === "district" && activeTab === "ward") {
        setWards([]);
      }
    },
    [activeTab]
  );

  const fetchProvinces = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE_URL}/`);
      if (!res.ok) {
        throw new Error(`Failed to fetch provinces: ${res.status}`);
      }
      const data = await res.json();
      setProvinces(data);
    } catch (e) {
      console.error("province fetching error ", e);
      setError("Failed to load provinces. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchDistricts = useCallback(async (provinceCode) => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE_URL}/p/${provinceCode}?depth=2`);
      if (!res.ok) {
        throw new Error(`Failed to fetch districts: ${res.status}`);
      }
      const data = await res.json();
      setDistricts(data.districts || []);
    } catch (error) {
      console.error("Error fetching districts", error);
      setError("Failed to load districts. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchWards = useCallback(async (districtCode) => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE_URL}/d/${districtCode}?depth=2`);
      if (!res.ok) {
        throw new Error(`Failed to fetch wards: ${res.status}`);
      }
      const data = await res.json();
      setWards(data.wards || []);
    } catch (error) {
      console.error("Error fetching wards", error);
      setError("Failed to load wards. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleOptionClick = useCallback(
    async (option) => {
      if (activeTab === "province") {
        setSelectedProvince(option);
        clearLocationData();
        await fetchDistricts(option.code);
        onAddressChange(option.name, "province", option);
        setActiveTab("district");
      } else if (activeTab === "district") {
        setSelectedDistrict(option);
        await fetchWards(option.code);
        onAddressChange(
          `${option.name}, ${selectedProvince.name}`,
          "district",
          option
        );
        setActiveTab("ward");
      } else if (activeTab === "ward") {
        const fullAddress = `${option.name}, ${selectedDistrict.name}, ${selectedProvince.name}`;
        onAddressChange(fullAddress, "ward", option);
        onClose();
      }
    },
    [
      activeTab,
      fetchDistricts,
      fetchWards,
      onAddressChange,
      selectedProvince,
      selectedDistrict,
      onClose,
    ]
  );

  const filteredOptions = useCallback(() => {
    let options = [];

    if (activeTab === "province") {
      options = provinces.map((province) => ({
        name: province.name,
        code: province.code,
      }));
    } else if (activeTab === "district") {
      options = districts.map((district) => ({
        name: district.name,
        code: district.code,
      }));
    } else {
      options = wards.map((ward) => ({ name: ward.name, code: ward.code }));
    }

    const normalizedSearchText = searchText
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase();

    return options.filter((option) =>
      option.name
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase()
        .includes(normalizedSearchText)
    );
  }, [activeTab, provinces, districts, wards, searchText]);

  useEffect(() => {
    fetchProvinces();
  }, [fetchProvinces]);

  const [isMobile, setIsMobile] = useState(window.innerWidth <= 960);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 960);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="fixed md:absolute inset-x-0 bottom-0 md:bottom-auto md:top-full left-0 md:left-0 h-[75vh] md:h-auto bg-[#FFFFFF] md:bg-[#FFFFFF] md:mt-1 rounded-t-2xl md:rounded-lg md:border border-[#E9EAEB] shadow-xl md:shadow-md overflow-hidden">
      {/* Header - Only show on mobile */}
      {isMobile && (
        <div className="px-4">
          <div className="w-8 h-1 bg-[#D9D9D9] rounded-lg mx-auto mt-2" />
          <div className="pt-2 flex items-center justify-between px-2">
            <div className="flex-1" />
            <h3 className="flex-1 text-base font-semibold text-[#181D27] text-center">
              Chọn vị trí
            </h3>
            <div className="flex-1 flex justify-end">
              <button onClick={onClose}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <path
                    d="M15 5L5 15M5 5L15 15"
                    stroke="#A4A7AE"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div className="pt-4 pb-2">
            <input
              type="text"
              value={currentAddress || ""}
              readOnly
              className="w-full px-[14px] py-[10px] border-[#D5D7DA] border rounded-lg text-sm text-[#717680] bg-[#FFFFFF]"
              placeholder="Chọn vị trí"
            />
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-[#E9EAEB] gap-x-3 pt-3">
          <div
            className={`flex-1 pl-3 py-2 text-center cursor-pointer text-[#717680] text-sm ${
              activeTab === "province"
                ? "border-b-2 border-[#299D55] font-semibold text-[#198C43]"
                : ""
            }`}
            onClick={() => handleTabChange("province")}
          >
            Tỉnh/Thành phố
          </div>
          <div
            className={`flex-1 py-2 text-center cursor-pointer text-[#717680] text-sm ${
              activeTab === "district"
                ? "border-b-2 border-[#299D55] font-semibold text-[#198C43]"
                : ""
            }`}
            onClick={() => handleTabChange("district")}
          >
            Quận/Huyện
          </div>
          <div
            className={`flex-1 pr-3 py-2  text-center cursor-pointer text-[#717680] text-sm ${
              activeTab === "ward"
                ? "border-b-2 border-[#299D55] font-semibold text-[#198C43]"
                : ""
            }`}
            onClick={() => handleTabChange("ward")}
          >
            Phường/Xã
          </div>
        </div>
      </div>

      {/* Search input - Only show on desktop */}
      {!isMobile && (
        <div className="relative px-[14px] py-3">
          <div className="relative">
            <div className="absolute left-[14px] top-1/2 -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="group-focus-within:stroke-[#299D55]"
              >
                <path
                  d="M17.5 17.5L14.5834 14.5833M16.6667 9.58333C16.6667 13.4954 13.4954 16.6667 9.58333 16.6667C5.67132 16.6667 2.5 13.4954 2.5 9.58333C2.5 5.67132 5.67132 2.5 9.58333 2.5C13.4954 2.5 16.6667 5.67132 16.6667 9.58333Z"
                  stroke="#717680"
                  strokeWidth="1.66667"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="group focus-within:ring-2 focus-within:ring-[#299D55] rounded-lg">
              <input
                type="text"
                placeholder="Tìm kiếm"
                value={searchText}
                onChange={handleSearchChange}
                className="w-full px-[14px] py-[10px] pl-[44px] border border-[#D5D7DA] rounded-lg text-base text-[#181D27] pr-10 focus:outline-none"
              />
            </div>
            {searchText && (
              <button
                onClick={() => setSearchText("")}
                className="absolute right-[14px] top-1/2 -translate-y-1/2 focus:outline-none"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  className="hover:fill-[#535862] focus:fill-[#299D55]"
                >
                  <path
                    d="M10 1.66699C5.39683 1.66699 1.66663 5.39824 1.66663 10.0003C1.66663 14.6024 5.39683 18.3337 10 18.3337C14.603 18.3337 18.3332 14.6024 18.3332 10.0003C18.3332 5.39824 14.603 1.66699 10 1.66699ZM13.8613 12.3889C14.0566 12.5842 14.1664 12.8491 14.1664 13.1253C14.1664 13.4016 14.0566 13.6665 13.8613 13.8618C13.666 14.0571 13.4011 14.1668 13.1249 14.1668C12.8486 14.1668 12.5837 14.0571 12.3884 13.8618L10 11.4732L7.61142 13.8618C7.5149 13.9589 7.40015 14.0359 7.27376 14.0884C7.14738 14.141 7.01184 14.1681 6.87496 14.1681C6.73808 14.1681 6.60254 14.141 6.47615 14.0884C6.34977 14.0359 6.23502 13.9589 6.1385 13.8618C5.94322 13.6664 5.83352 13.4015 5.83352 13.1253C5.83352 12.8491 5.94322 12.5842 6.1385 12.3889L8.52704 10.0003L6.1385 7.61178C5.94318 7.41646 5.83345 7.15155 5.83345 6.87533C5.83345 6.5991 5.94318 6.33419 6.1385 6.13887C6.33382 5.94355 6.59873 5.83382 6.87496 5.83382C7.15119 5.83382 7.4161 5.94355 7.61142 6.13887L10 8.52741L12.3884 6.13887C12.5837 5.94355 12.8486 5.83382 13.1249 5.83382C13.4011 5.83382 13.666 5.94355 13.8613 6.13887C14.0566 6.33419 14.1664 6.5991 14.1664 6.87533C14.1664 7.15155 14.0566 7.41646 13.8613 7.61178L11.4728 10.0003L13.8613 12.3889Z"
                    fill="#717680"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Options list */}
      <div
        className={`overflow-y-auto px-4 ${
          isMobile ? "max-h-[45vh] md:max-h-[280px]" : "max-h-[280px]"
        }`}
      >
        {isLoading ? (
          <div className="p-4 text-center text-sm text-[#717680]">
            Đang tải...
          </div>
        ) : error ? (
          <div className="p-4 text-center text-sm text-red-500">{error}</div>
        ) : (
          filteredOptions().map((option, index) => (
            <div
              key={index}
              className="p-2 cursor-pointer hover:bg-[#F9FAFB] flex items-center justify-between"
              onClick={() => handleOptionClick(option)}
            >
              <span className="text-base font-medium text-[#181D27]">
                {option.name}
              </span>
              {((activeTab === "province" &&
                selectedProvince?.code === option.code) ||
                (activeTab === "district" &&
                  selectedDistrict?.code === option.code)) && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <path
                    d="M16.6666 5L7.49992 14.1667L3.33325 10"
                    stroke="#299D55"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default LocationSelector;
