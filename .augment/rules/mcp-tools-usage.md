---
type: "always_apply"
---

# MCP Tools Usage Guidelines - Ông Ba Dạy Hóa Project

## 🎯 MCP Tools Integration Strategy
You have access to powerful MCP (Model Context Protocol) tools that should be leveraged strategically for the Ông Ba Dạy Hóa project development.

## 🔧 Available MCP Tools & When to Use

### 1. **Context7 Library Research**
**Tools:** `resolve-library-id_Context_7`, `get-library-docs_Context_7`

**Use for:**
- Researching Next.js 15 App Router patterns and best practices
- Learning Strapi 5 advanced features and TypeScript integration
- Finding MySQL optimization techniques for educational platforms
- Discovering security best practices for authentication and payments
- Exploring integration patterns between technologies

**Example Usage:**
```
When implementing new features:
1. Use resolve-library-id to find relevant documentation
2. Use get-library-docs to get specific implementation details
3. Apply learnings to project-specific context
```

### 2. **Web Research & Documentation**
**Tools:** `firecrawl_scrape`, `firecrawl_search`, `brave_web_search`, `brave_local_search`

**Use for:**
- Researching Dokploy deployment best practices
- Finding Cloudflare R2 integration patterns
- Learning about PayOS payment gateway implementation
- Discovering Vietnamese market-specific requirements
- Staying updated with latest tech stack developments

**Priority Order:**
1. `brave_web_search` - For general technical research
2. `firecrawl_search` - For deep technical documentation
3. `firecrawl_scrape` - For specific documentation pages
4. `brave_local_search` - For Vietnamese market research

### 3. **Codebase Analysis & Development**
**Tools:** `codebase-retrieval`, `git-commit-retrieval`, `str-replace-editor`, `save-file`

**Use for:**
- Understanding existing patterns before implementing new features
- Finding similar implementations in the codebase
- Learning from previous changes and commits
- Implementing new features following project conventions

**Workflow:**
1. Always use `codebase-retrieval` before making changes
2. Use `git-commit-retrieval` to understand how similar features were implemented
3. Use `str-replace-editor` for modifications, `save-file` for new files

### 4. **Browser Automation & Testing**
**Tools:** `browser_navigate`, `browser_snapshot`, `browser_click`, etc.

**Use for:**
- Testing deployed applications on production/staging
- Verifying UI/UX implementations
- Debugging frontend issues
- Validating responsive design

## 🎯 Project-Specific MCP Usage Patterns

### For Next.js Development:
1. Research with Context7: "Next.js 15 App Router"
2. Find specific patterns: Server Components, Client Components
3. Apply to Vietnamese URLs: /dang-nhap, /khoa-hoc, /quan-ly
4. Test with browser tools for responsive design

### For Strapi Development:
1. Research with Context7: "Strapi 5 TypeScript"
2. Find content type patterns and relationships
3. Implement custom controllers/services
4. Test API endpoints with browser tools

### For Database Optimization:
1. Research with Context7: "MySQL performance optimization"
2. Find indexing strategies for educational platforms
3. Apply to current schema with 1K+ users in mind
4. Monitor performance with appropriate tools

### For Infrastructure & Deployment:
1. Research Dokploy best practices with web search
2. Find Cloudflare R2 integration patterns
3. Implement following current deployment workflow
4. Test deployment process

## 🚀 MCP Tools Workflow for Common Tasks

### Task 1: Implementing New Feature
1. codebase-retrieval: Analyze existing similar features
2. git-commit-retrieval: Learn from previous implementations
3. Context7: Research best practices for the technology
4. Web search: Find additional resources if needed
5. str-replace-editor: Implement following project patterns
6. Browser tools: Test the implementation

### Task 2: Debugging Issues
1. codebase-retrieval: Understand the problematic code
2. git-commit-retrieval: Check recent changes that might cause issues
3. Browser tools: Reproduce and analyze the issue
4. Web search: Find solutions for similar problems
5. str-replace-editor: Apply fixes
6. Browser tools: Verify the fix works

### Task 3: Performance Optimization
1. Context7: Research optimization techniques
2. codebase-retrieval: Analyze current implementation
3. Web search: Find specific optimization strategies
4. str-replace-editor: Implement optimizations
5. Browser tools: Test performance improvements

### Task 4: Security Hardening
1. Context7: Research security best practices
2. Web search: Find latest security vulnerabilities and fixes
3. codebase-retrieval: Audit current security implementations
4. str-replace-editor: Apply security improvements
5. Browser tools: Test security measures

## 🛡️ MCP Tools Best Practices

### Research Strategy:
- **Start broad, then narrow:** Use web search first, then Context7 for specifics
- **Verify information:** Cross-reference multiple sources
- **Apply to context:** Always consider project-specific requirements
- **Document findings:** Save important discoveries for future reference

### Development Strategy:
- **Understand first:** Always analyze existing code before changes
- **Follow patterns:** Use codebase-retrieval to maintain consistency
- **Test thoroughly:** Use browser tools to verify implementations
- **Consider scale:** Remember the 1K user base and growth trajectory

### Security Strategy:
- **Research first:** Use Context7 and web search for security best practices
- **Audit regularly:** Use codebase-retrieval to check for vulnerabilities
- **Test security:** Use browser tools to verify security measures
- **Stay updated:** Regular web search for new security threats

## 📊 MCP Tools Success Metrics

### Research Effectiveness:
- Find relevant, up-to-date information quickly
- Apply research findings to project successfully
- Reduce implementation time through better preparation

### Development Efficiency:
- Maintain code consistency across the project
- Reduce bugs through better understanding of existing code
- Implement features that integrate well with existing architecture

### Quality Assurance:
- Catch issues early through thorough testing
- Ensure responsive design works across devices
- Verify security measures are properly implemented

## 🎪 Communication About MCP Usage

### When Using MCP Tools:
- **Explain the research:** Share what you learned and why it's relevant
- **Show the process:** Explain how you used the tools to reach conclusions
- **Provide context:** Connect findings to project-specific requirements
- **Suggest next steps:** Recommend follow-up actions based on findings

### Example Communication:
"Tôi đã research Next.js 15 App Router patterns với Context7 và tìm thấy best practices cho Server Components. Dựa vào codebase analysis, tôi thấy chúng ta có thể optimize trang /khoa-hoc bằng cách implement Server Components cho course listing. Tôi sẽ test implementation này với browser tools để đảm bảo performance tốt cho 1K users hiện tại."

---

*Use MCP tools strategically to enhance development efficiency while maintaining the high quality standards of the Ông Ba Dạy Hóa project.*
