# Ông Ba Dạy Hóa - Nền tảng học hóa học trực tuyến

## Tổng quan dự án

Ông Ba Dạy Hóa là một nền tảng học hóa học trực tuyến được thiết kế để hỗ trợ học sinh trong việc học tập và nâng cao kiến thức hóa học. Dự án sử dụng kiến trúc hiện đại với Next.js cho phần frontend và Strapi CMS cho phần backend, tạo thành một hệ thống headless CMS đầy đủ tính năng.

## Công nghệ sử dụng

### Frontend
- **Next.js 15**: Framework React hiện đại với App Router, hỗ trợ server và client components
- **React 19**: Thư viện UI với hooks và functional components
- **Tailwind CSS**: Framework CSS để xây dựng giao diện với cấu hình tùy chỉnh (xem `tailwind.config.js`)
- **Axios**: Thư viện HTTP client để giao tiếp với API, được cấu hình với interceptors để xử lý authentication
- **React Cookie**: Quản lý cookies cho authentication
- **React Hot Toast**: Hiển thị thông báo
- **React Icons, React Spinners**: Các thư viện UI components
- **Google OAuth**: Tích hợp đăng nhập với Google thông qua `@react-oauth/google`

### Backend
- **Strapi 5**: Headless CMS cung cấp API và quản lý nội dung
- **MySQL**: Cơ sở dữ liệu quan hệ (cấu hình trong `config/database.ts`)
- **Node.js**: Môi trường runtime (yêu cầu Node.js >=18.0.0 <=22.x.x)
- **TypeScript**: Ngôn ngữ lập trình với type checking
- **Strapi Plugins**:
  - `users-permissions`: Quản lý người dùng và phân quyền
  - `strapi-plugin-sso`: Hỗ trợ đăng nhập một lần
  - `strapi-provider-email-brevo`: Gửi email thông qua Brevo (SendinBlue)

## Kiến trúc hệ thống

### Luồng dữ liệu
1. **Client Request**: Người dùng tương tác với giao diện Next.js
2. **API Layer**: Next.js gọi API từ Strapi thông qua Axios
3. **Authentication**: JWT-based với token được lưu trong cookies
4. **Data Processing**: Strapi xử lý request, tương tác với database
5. **Response**: Dữ liệu được trả về client và render

### Cấu trúc API
- RESTful API endpoints được Strapi tự động tạo ra cho mỗi content type
- Custom controllers và services cho các tính năng đặc biệt (payment, authentication, etc.)
- API được bảo vệ bởi JWT và CORS policies (xem `config/middlewares.ts`)

## Chi tiết Frontend (Next.js)

### Cấu trúc thư mục
```
frontend/
├── app/                      # App router và components
│   ├── api/                  # API routes và client wrappers
│   │   ├── auth/             # Authentication API routes
│   │   │   ├── login/        # Đăng nhập
│   │   │   ├── register/     # Đăng ký
│   │   │   ├── send-otp/     # Gửi OTP
│   │   │   ├── verify-otp/   # Xác thực OTP
│   │   │   └── ...
│   │   └── strapi.js         # Axios client wrapper cho Strapi API
│   ├── auth/                 # Authentication pages
│   │   ├── dang-nhap/        # Trang đăng nhập
│   │   ├── dang-ky/          # Trang đăng ký
│   │   └── ...
│   ├── bai-viet/             # Trang blog
│   ├── hoa-don/              # Trang hóa đơn
│   ├── khoa-hoc/             # Trang khóa học
│   ├── quan-ly/              # Trang quản lý học tập
│   ├── tai-khoan/            # Trang tài khoản
│   ├── thanh-toan/           # Trang thanh toán
│   ├── thong-bao/            # Trang thông báo
│   ├── thong-tin-ca-nhan/    # Trang thông tin cá nhân
│   ├── layout.js             # Root layout
│   └── page.js               # Trang chủ
│   ├── providers.js          # React context providers
├── components/               # Shared React components
│   ├── ui/                   # UI components (buttons, inputs, etc.)
│   ├── forms/                # Form components
│   ├── layout/               # Layout components (header, footer, etc.)
│   └── course/               # Course-specific components
├── data/                     # Static data (schools, etc.)
├── public/                   # Static assets
├── middleware.js             # Next.js middleware (auth, redirects)
├── next.config.mjs           # Next.js configuration
├── tailwind.config.js        # Tailwind CSS configuration
└── package.json              # Dependencies and scripts
```

### Các trang chính
- **Trang chủ (`/`)**: Giới thiệu về nền tảng, hiển thị khóa học nổi bật
- **Khóa học (`/khoa-hoc`)**: Danh sách khóa học, lọc theo cấp học
- **Chi tiết khóa học (`/khoa-hoc/[id]`)**: Thông tin chi tiết về khóa học
- **Blog (`/bai-viet`)**: Danh sách bài viết
- **Chi tiết bài viết (`/bai-viet/[id]`)**: Nội dung chi tiết bài viết
- **Đăng nhập (`/dang-nhap`)**: Đăng nhập với email/password hoặc Google
- **Đăng ký (`/dang-ky`)**: Đăng ký tài khoản mới
- **Quản lý (`/quan-ly`)**: Dashboard học tập cho học viên
- **Thanh toán (`/thanh-toan`)**: Trang thanh toán khóa học
- **Hóa đơn (`/hoa-don`)**: Xem và tải hóa đơn

### Components chính
- **Header**: Navigation, user menu, responsive design
- **Footer**: Links, contact info
- **CourseCard**: Hiển thị thông tin khóa học
- **BlogCard**: Hiển thị thông tin bài viết
- **AuthForms**: Forms đăng nhập, đăng ký
- **PaymentForm**: Form thanh toán

### Context Providers
- **AuthProvider**: Quản lý trạng thái đăng nhập
- **UserProvider**: Quản lý thông tin người dùng
- **NotificationProvider**: Quản lý thông báo

### API Client
File `app/api/strapi.js` là wrapper cho Strapi API, cung cấp các phương thức:
- **auth**: Đăng nhập, đăng ký, xác thực
- **courses**: Lấy thông tin khóa học
- **blog**: Lấy thông tin bài viết
- **payment**: Xử lý thanh toán
- **orders**: Quản lý đơn hàng
- **sendOTP**: Gửi và xác thực OTP

### Middleware
File `middleware.js` xử lý:
- **Authentication**: Kiểm tra đăng nhập
- **Redirects**: Chuyển hướng dựa trên trạng thái đăng nhập
- **User Info**: Kiểm tra thông tin cá nhân

## Chi tiết Backend (Strapi)

### Cấu trúc thư mục
```
backend/
├── src/                      # Source code
│   ├── api/                  # API endpoints và models
│   │   ├── course/           # Course API
│   │   │   ├── content-types/# Course model
│   │   │   ├── controllers/  # Course controllers
│   │   │   ├── routes/       # Course routes
│   │   │   └── services/     # Course services
│   │   ├── chapter/          # Chapter API
│   │   ├── knowledge/        # Knowledge API
│   │   ├── exercise/         # Exercise API
│   │   ├── blog-post/        # Blog API
│   │   ├── payment/          # Payment API
│   │   ├── order/            # Order API
│   │   ├── send-otp/         # OTP API
│   │   └── ...
│   ├── email-templates/      # Templates email (HTML)
│   │   ├── OTP.html          # OTP verification email
│   │   ├── PaymentConfirmation.html # Xác nhận thanh toán
│   │   └── ...
│   └── index.ts              # Entry point
├── config/                   # Cấu hình Strapi
│   ├── admin.ts              # Admin panel configuration
│   ├── database.ts           # Database connection
│   ├── middlewares.ts        # Middleware configuration
│   ├── plugins.ts            # Plugin configuration
│   └── server.ts             # Server configuration
├── public/                   # Public assets
├── dist/                     # Compiled code
├── ecosystem.config.js       # PM2 configuration
├── package.json              # Dependencies and scripts
└── tsconfig.json             # TypeScript configuration
```

## Biến môi trường

### Backend (.env)
```
HOST=0.0.0.0
PORT=1337

APP_KEYS=<your_app_keys>
API_TOKEN_SALT=<your_api_token_salt>
ADMIN_JWT_SECRET=<your_admin_jwt_secret>
TRANSFER_TOKEN_SALT=<your_transfer_token_salt>
JWT_SECRET=<your_jwt_secret>

# Database
DATABASE_HOST=<your_database_host>
DATABASE_PORT=<your_database_port>
DATABASE_NAME=<your_database_name>
DATABASE_USERNAME=<your_database_username>
DATABASE_PASSWORD=<your_database_password>
DATABASE_SSL=<your_database_ssl>

# SendMail
SENDINBLUE_API_KEY=<your_sendinblue_api_key>

# Google
GOOGLE_CLIENT_ID=<your_google_client_id>
GOOGLE_CLIENT_SECRET=<your_google_client_secret>
GOOGLE_REDIRECT_URI=<your_google_redirect_uri>

# Payos
PAYOS_CLIENT_ID=<your_payos_client_id>
PAYOS_API_KEY=<your_payos_api_key>
PAYOS_CHECKSUM_KEY=<your_payos_checksum_key>
PAYOS_API_URL=<your_payos_api_url>
BACKEND_URL=<your_backend_url>
```

### Frontend (.env)
```
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337/api
NEXT_PUBLIC_STRAPI_PROTOCOL=http
NEXT_PUBLIC_STRAPI_HOST=localhost
NEXT_PUBLIC_STRAPI_PORT=1337
NEXT_PUBLIC_GOOGLE_CLIENT_ID=<your_google_client_id>
```

## Hướng dẫn cài đặt chi tiết

### Backend (Strapi)

1. **Cài đặt dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Cấu hình biến môi trường**
   - Tạo file `.env` từ `.env.example`
   - Điền các thông tin cần thiết

3. **Khởi động development server**
   ```bash
   npm run develop
   ```
   Server sẽ chạy tại http://localhost:1337 với Admin panel tại http://localhost:1337/admin

4. **Build cho production**
   ```bash
   npm run build
   npm run start
   ```

5. **Triển khai với PM2**
   ```bash
   pm2 start ecosystem.config.js
   ```

### Frontend (Next.js)

1. **Cài đặt dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Cấu hình biến môi trường**
   - Tạo file `.env` với các biến cần thiết

3. **Khởi động development server**
   ```bash
   npm run dev
   ```
   Server sẽ chạy tại http://localhost:3000

4. **Build cho production**
   ```bash
   npm run build
   npm run start
   ```

## Quy trình phát triển

### Quy trình làm việc với Strapi
1. **Content Types**: Tạo và cấu hình content types trong Strapi Admin
2. **API Permissions**: Cấu hình quyền truy cập API trong Users & Permissions plugin
3. **Custom Logic**: Viết controllers và services tùy chỉnh trong `src/api/*/controllers` và `src/api/*/services`
4. **Build**: Chạy `npm run build` sau khi thay đổi cấu trúc

### Quy trình làm việc với Next.js
1. **Components**: Phát triển UI components trong thư mục `components/`
2. **Pages**: Tạo pages trong thư mục `app/`
3. **API Integration**: Sử dụng Axios client trong `app/api/strapi.js` để gọi API
4. **Styling**: Sử dụng Tailwind CSS với các class được định nghĩa trong `tailwind.config.js`

## Triển khai

### Backend
- **Server Requirements**: Node.js >=18.0.0 <=22.x.x, MySQL
- **Process Manager**: PM2 với file cấu hình `ecosystem.config.js`
- **Database**: MySQL với cấu hình trong `config/database.ts`
- **CORS**: Cấu hình trong `config/middlewares.ts` để cho phép các domain cụ thể

### Frontend
- **Hosting Options**: Vercel, Netlify, hoặc bất kỳ hosting nào hỗ trợ Next.js
- **Environment Variables**: Cấu hình các biến môi trường cần thiết
- **Static Assets**: Được phục vụ từ thư mục `public/`

## Bảo trì và cập nhật

### Strapi
- Kiểm tra cập nhật: `npm outdated`
- Cập nhật Strapi: Xem hướng dẫn cập nhật chính thức
- Backup database trước khi cập nhật

### Next.js
- Cập nhật dependencies: `npm update`
- Kiểm tra breaking changes trong Next.js changelog
- Chạy tests sau khi cập nhật

## Xử lý sự cố

### Backend
- Kiểm tra logs: `pm2 logs`
- Kiểm tra kết nối database
- Xác minh cấu hình CORS
- Kiểm tra quyền truy cập API

### Frontend
- Kiểm tra console errors
- Xác minh API endpoints
- Kiểm tra authentication state
- Xác minh biến môi trường

## Liên hệ và hỗ trợ

- **Email hỗ trợ**: <EMAIL>
- **Tài liệu Strapi**: https://docs.strapi.io
- **Tài liệu Next.js**: https://nextjs.org/docs
